import React, { useState } from "react";
import { Controller } from "react-hook-form";
import { Card, CardContent } from "@base/card";
import { RadioGroup, RadioGroupItem } from "@base/radio-group";
import { Label } from "@base/label";
import { Badge } from "@commons/badge";
import { Button } from "@base/button";
import { Plus } from "lucide-react";
import { usePaymentMethods } from "@/hooks/usePaymentMethods";
import { useTranslation } from "@hooks/useTranslation";
import { PaymentMethodForm } from "@/components/payment-methods/payment-method-form";
import { PaymentMethodType } from "@/types/payment-methods";
import { MOBILE_BANKING_OPTIONS } from "@/pages/checkout/constants";
import type { PaymentMethodProps } from "./types";
import {
  handleCardClick,
  getPaymentMethodErrorMessage,
  getPaymentMethodIconType,
  getSavedMethodDisplayText
} from "./helpers";

const PaymentMethod: React.FC<PaymentMethodProps> = ({ control, errors }) => {
  const { t } = useTranslation();
  const { paymentMethods, addPaymentMethod } = usePaymentMethods();
  const [showAddForm, setShowAddForm] = useState(false);
  const [selectedType, setSelectedType] = useState<PaymentMethodType | undefined>();

  // Helper function to render payment method icons
  const renderPaymentIcon = (type: string) => {
    const iconType = getPaymentMethodIconType(type);
    const iconProps = { className: "h-5 w-5" };

    switch (iconType) {
      case 'CreditCard':
        return <CreditCard {...iconProps} />;
      case 'Wallet':
        return <Wallet {...iconProps} />;
      case 'Smartphone':
        return <Smartphone {...iconProps} />;
      case 'QrCode':
        return <QrCode {...iconProps} />;
      case 'Building2':
        return <Building2 {...iconProps} />;
      default:
        return <CreditCard {...iconProps} />;
    }
  };

  const handleAddNewMethod = (type: PaymentMethodType) => {
    setSelectedType(type);
    setShowAddForm(true);
  };

  const handleFormSubmit = async (type: PaymentMethodType, formData: any) => {
    try {
      await addPaymentMethod(type, formData);
      setShowAddForm(false);
      setSelectedType(undefined);
    } catch (error) {
      console.error("Error adding payment method:", error);
    }
  };

  const handleFormCancel = () => {
    setShowAddForm(false);
    setSelectedType(undefined);
  };

  if (showAddForm && selectedType) {
    return (
      <div>
        <PaymentMethodForm
          type={selectedType}
          isEdit={false}
          onSubmit={handleFormSubmit}
          onCancel={handleFormCancel}
        />
      </div>
    );
  }

  return (
    <div>
      <h2 className="text-[23px] font-medium text-black">
        {t("payment.methods.title")}
      </h2>
      <p className="mb-4 text-sm text-gray-500">
        การทำธุรกรรมทั้งหมดปลอดภัยและมีการเข้ารหัส
      </p>

      <div className="space-y-4">
        <Controller
          name="paymentMethod"
          control={control}
          render={({ field }) => (
            <RadioGroup
              value={field.value}
              onValueChange={(value) => {
                field.onChange(value);
              }}
            >
              {/* Saved Payment Methods */}
              {paymentMethods.length > 0 && (
                <div className="space-y-3">
                  <h3 className="text-sm font-medium text-gray-700">
                    {t("payment.methods.current")}
                  </h3>
                  {paymentMethods.map((method) => (
                    <Card
                      key={`saved_${method.id}`}
                      className={`cursor-pointer transition-colors ${
                        field.value === `saved_${method.id}`
                          ? "border-primary bg-primary/5"
                          : "border-gray-200"
                      }`}
                      onClick={(e) => handleCardClick(e, field.onChange, `saved_${method.id}`)}
                    >
                      <CardContent className="p-4">
                        <div className="flex items-center space-x-3">
                          <RadioGroupItem value={`saved_${method.id}`} id={`saved_${method.id}`} />
                          <div className="flex items-center space-x-3">
                            {renderPaymentIcon(method.type)}
                            <div className="flex-1">
                              <div className="flex items-center gap-2">
                                <Label
                                  htmlFor={`saved_${method.id}`}
                                  className="cursor-pointer font-medium"
                                >
                                  {getSavedMethodDisplayText(method)}
                                </Label>
                                {method.isDefault && (
                                  <Badge variant="outline" className="text-xs">
                                    {t("payment.methods.main")}
                                  </Badge>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}

              {/* New Payment Method Options */}
              <div className="space-y-3">
                <h3 className="text-sm font-medium text-gray-700">
                  วิธีการชำระเงิน
                </h3>

                {/* Credit/Debit Card */}
                <Card
                  className={`cursor-pointer transition-colors ${
                    field.value === "new_credit_card"
                      ? "border-primary bg-primary/5"
                      : "border-gray-200"
                  }`}
                  onClick={(e) => handleCardClick(e, field.onChange, "new_credit_card")}
                >
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <RadioGroupItem value="new_credit_card" id="new_credit_card" />
                        <div className="flex items-center space-x-3">
                          {renderPaymentIcon('credit_card')}
                          <Label htmlFor="new_credit_card" className="cursor-pointer font-medium">
                            บัตรเครดิต/เดบิต
                          </Label>
                        </div>
                      </div>
                      {field.value === "new_credit_card" && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleAddNewMethod(PaymentMethodType.CREDIT_CARD);
                          }}
                        >
                          <Plus className="h-4 w-4 mr-1" />
                          เพิ่มบัตร
                        </Button>
                      )}
                    </div>
                  </CardContent>
                </Card>


                {/* Thai QR */}
                <Card
                  className={`cursor-pointer transition-colors ${
                    field.value === "thai_qr"
                      ? "border-primary bg-primary/5"
                      : "border-gray-200"
                  }`}
                  onClick={(e) => handleCardClick(e, field.onChange, "thai_qr")}
                >
                  <CardContent className="p-4">
                    <div className="flex items-center space-x-3">
                      <RadioGroupItem value="thai_qr" id="thai_qr" />
                      <div className="flex items-center space-x-3">
                        {renderPaymentIcon('thai_qr')}
                        <Label htmlFor="thai_qr" className="cursor-pointer font-medium">
                          Thai QR Payment
                        </Label>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Mobile Banking */}
                <Card
                  className={`cursor-pointer transition-colors ${
                    field.value === "mobile_banking"
                      ? "border-primary bg-primary/5"
                      : "border-gray-200"
                  }`}
                  onClick={(e) => handleCardClick(e, field.onChange, "mobile_banking")}
                >
                  <CardContent className="p-4">
                    <div className="flex items-center space-x-3">
                      <RadioGroupItem value="mobile_banking" id="mobile_banking" />
                      <div className="flex items-center space-x-3">
                        {renderPaymentIcon('mobile_banking')}
                        <Label htmlFor="mobile_banking" className="cursor-pointer font-medium">
                          Mobile Banking
                        </Label>
                      </div>
                    </div>

                    {/* Mobile Banking Options */}
                    {field.value === "mobile_banking" && (
                      <div className="mt-4 pl-7">
                        <p className="mb-2 text-sm text-gray-600">
                          เลือกธนาคาร:
                        </p>
                        <div className="grid grid-cols-2 gap-2">
                          {MOBILE_BANKING_OPTIONS.map((bank) => (
                            <div
                              key={bank}
                              className="rounded bg-gray-50 p-2 text-sm hover:bg-gray-100 cursor-pointer"
                            >
                              {bank}
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* TrueMoney Wallet */}
                <Card
                  className={`cursor-pointer transition-colors ${
                    field.value === "new_truemoney"
                      ? "border-primary bg-primary/5"
                      : "border-gray-200"
                  }`}
                  onClick={(e) => handleCardClick(e, field.onChange, "new_truemoney")}
                >
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <RadioGroupItem value="new_truemoney" id="new_truemoney" />
                        <div className="flex items-center space-x-3">
                          {renderPaymentIcon('truemoney')}
                          <Label htmlFor="new_truemoney" className="cursor-pointer font-medium">
                            TrueMoney Wallet
                          </Label>
                        </div>
                      </div>
                      {field.value === "new_truemoney" && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleAddNewMethod(PaymentMethodType.TRUEMONEY);
                          }}
                        >
                          <Plus className="h-4 w-4 mr-1" />
                          เพิ่มกระเป๋าเงิน
                        </Button>
                      )}
                    </div>
                  </CardContent>
                </Card>

                {/* AliPay */}
                <Card
                  className={`cursor-pointer transition-colors ${
                    field.value === "new_alipay"
                      ? "border-primary bg-primary/5"
                      : "border-gray-200"
                  }`}
                  onClick={(e) => handleCardClick(e, field.onChange, "new_alipay")}
                >
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <RadioGroupItem value="new_alipay" id="new_alipay" />
                        <div className="flex items-center space-x-3">
                          {renderPaymentIcon('alipay')}
                          <Label htmlFor="new_alipay" className="cursor-pointer font-medium">
                            AliPay
                          </Label>
                        </div>
                      </div>
                      {field.value === "new_alipay" && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleAddNewMethod(PaymentMethodType.ALIPAY);
                          }}
                        >
                          <Plus className="h-4 w-4 mr-1" />
                          เพิ่มบัญชี
                        </Button>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </RadioGroup>
          )}
        />

        {errors.paymentMethod && (
          <p className="mt-2 text-xs text-red-600">
            {getPaymentMethodErrorMessage(errors.paymentMethod)}
          </p>
        )}
      </div>
    </div>
  );
};

export default PaymentMethod;
